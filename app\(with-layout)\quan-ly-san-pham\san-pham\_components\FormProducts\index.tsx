'use client';

import { useDeleteProduct } from '@/apis/product/product.api';
import { IProduct } from '@/apis/product/product.type';
import FormController from '@/components/common/FormController';
import { commonStatus } from '@/constants/sharedData/sharedData';
import useGetOptionCategory from '@/hooks/useGetOptionsCategory';
import useGetOptionsSupplier from '@/hooks/useGetOptionsSupplier';
import { ROUTES } from '@/lib/routes';
import { showToastSuccess } from '@/utils/toast-message';
import { yupResolver } from '@hookform/resolvers/yup';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';
import * as yup from 'yup';
import TableProduct from './TableProduct';

const productFormSchema = yup.object().shape({
    name: yup
        .string()
        .required('Tên sản phẩm là trường bắt buộc')
        .min(2, 'Tên sản phẩm phải có ít nhất 2 ký tự')
        .max(100, 'Tên sản phẩm không được quá 100 ký tự')
        .trim('Tên sản phẩm không được có khoảng trắng ở đầu và cuối'),

    categoryId: yup.string().required('Danh mục sản phẩm là trường bắt buộc'),

    supplierId: yup.string().required('Nhà cung cấp là trường bắt buộc'),

    // Thêm các trường còn thiếu để khớp với IProduct interface - tất cả required theo interface
    code: yup.string().required(),
    description: yup.string().required(),
    productVersion: yup.string().required(),
    commonStatus: yup.number().required(),
    productOptions: yup.array().required(),
    id: yup.string().required(),
    categoryName: yup.string().required(),
    supplierName: yup.string().required(),
    status: yup.string().required(),
    userNameCreated: yup.string().required(),
    createdDateTime: yup.string().required(),
    installationType: yup.number().required(),
});

interface FormProductsProps {
    page: string;
    onSubmit?: (data: IProduct) => void;
    onClose?: () => void;
    initValue?: IProduct;
}

const FormProducts = ({
    page,
    onSubmit,
    onClose,
    initValue,
}: FormProductsProps) => {
    const router = useRouter();
    const formatDateTime = (dateTimeString: string): string => {
        if (!dateTimeString) {
            return '';
        }
        const datePart = dateTimeString.substring(0, 10);
        const [year, month, day] = datePart.split('-');
        return `${day}/${month}/${year}`;
    };
    const methods = useForm<IProduct>({
        defaultValues: {
            ...initValue,
            productOptions: initValue?.productOptions || [],
            createdDateTime: initValue?.createdDateTime
                ? formatDateTime(initValue.createdDateTime)
                : '',
            commonStatus: (initValue?.commonStatus?.toString() ||
                '') as unknown as number,
        },
        resolver: yupResolver(productFormSchema),
        mode: 'onChange',
    });
    const [image, setImage] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const handleFormSubmit = (data: IProduct) => {
        if (!data.productOptions || data.productOptions.length === 0) {
            toast.error('Phải có ít nhất một tùy chọn sản phẩm');
            return;
        }

        if (onSubmit) {
            onSubmit(data);
        }
    };

    const handleIconClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (ev) => {
                setImage(ev.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const categories = useGetOptionCategory();
    const suppliers = useGetOptionsSupplier();

    useEffect(() => {
        const statusText = methods.getValues('status');
        if (statusText === 'Đang hoạt động') {
            methods.setValue('commonStatus', '1' as unknown as number);
        } else if (statusText === 'Ngừng hoạt động') {
            methods.setValue('commonStatus', '2' as unknown as number);
        }
    }, [methods, initValue?.status]);

    const { mutate: deleteProduct } = useDeleteProduct({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa sản phẩm thành công',
                message:
                    'Thông tin sản phẩm đã được xóa thành công trong hệ thống.',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX);
        },
        onError: (error) => {
            const status = error.status;
            if (status === 400 || 401) {
                toast.warning(error.message);
                return;
            }
            toast.error(error.message);
        },
    });
    const handleDelete = () => {
        if (initValue?.id) {
            deleteProduct({ ids: [initValue.id], isDeleted: false });
        }
    };
    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                {page === 'chi-tiet' && (
                    <Row className='g-3 justify-content-around'>
                        <Col md={12} style={{ padding: '20px 40px 20px 40px' }}>
                            <div className='d-flex justify-content-end gap-3 align-items-center pe-3'>
                                <Button
                                    className='d-flex align-items-center gap-2 btn-outline-primary hover-primary'
                                    style={{
                                        padding: '4px 8px',
                                        fontSize: '12px',
                                        transition: 'all 0.2s ease',
                                        backgroundColor: 'transparent',
                                        border: '1px solid #0ab39c',
                                        color: '#0ab39c',
                                        borderRadius: '6px',
                                        height: '32px',
                                        fontWeight: 500,
                                    }}
                                    onClick={() =>
                                        router.push(
                                            ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.UPDATE.replace(
                                                ':id',
                                                initValue?.id,
                                            ),
                                        )
                                    }
                                >
                                    <i className='ri-pencil-line'></i>
                                    Chỉnh sửa
                                </Button>
                                <UncontrolledDropdown>
                                    <DropdownToggle
                                        tag='button'
                                        className='btn'
                                        style={{
                                            backgroundColor: '#0ab39c',
                                            border: 'none',
                                            padding: '4px',
                                            minWidth: '30px',
                                        }}
                                    >
                                        <i
                                            className='ri-more-fill'
                                            style={{
                                                color: 'white',
                                            }}
                                        ></i>
                                    </DropdownToggle>
                                    <DropdownMenu end>
                                        <DropdownItem>
                                            <i className='ri-history-line me-2'></i>
                                            Nhật ký nhóm sản phẩm
                                        </DropdownItem>
                                        <DropdownItem
                                            className='text-danger'
                                            onClick={handleDelete}
                                        >
                                            <i className='ri-delete-bin-line me-2'></i>
                                            Xóa
                                        </DropdownItem>
                                    </DropdownMenu>
                                </UncontrolledDropdown>
                            </div>
                        </Col>
                    </Row>
                )}
                <Row style={{ padding: '0px 40px 20px 40px' }}>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='code'
                            label='Mã sản phẩm'
                            placeholder='Mã sản phẩm'
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='name'
                            label='Tên sản phẩm'
                            placeholder='Tên sản phẩm'
                            required={true}
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            name='description'
                            controlType='textarea'
                            label='Mô tả chung'
                            readOnly={page === 'chi-tiet'}
                            rows={8}
                            placeholder='Mô tả chung về sản phẩm'
                        />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <TableProduct initValue={initValue} page={page} />
                    </Col>
                    <Col
                        md={12}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <Label>Hình ảnh</Label>
                        <div
                            style={{
                                position: 'relative',
                                width: '300px',
                                height: '200px',
                                display: 'flex',
                                justifyContent: 'center',
                                cursor: 'pointer',
                            }}
                            onClick={handleIconClick}
                        >
                            {image ? (
                                <Image
                                    src={image}
                                    alt='Preview'
                                    fill
                                    style={{
                                        objectFit: 'cover',
                                        marginLeft: '30px',
                                    }}
                                />
                            ) : (
                                <i
                                    className='ri-image-add-fill'
                                    style={{ fontSize: '100px', color: '#ccc' }}
                                ></i>
                            )}
                            <input
                                type='file'
                                accept='image/*'
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                onChange={handleFileChange}
                            />
                        </div>
                    </Col>
                    {page === 'chi-tiet' && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='textInput'
                                name='status'
                                label='Trạng thái'
                                placeholder='Trạng thái'
                                readOnly={page === 'chi-tiet'}
                            />
                        </Col>
                    )}
                    {(page === 'chinh-sua' || page === 'tao-moi') && (
                        <Col
                            md={6}
                            style={{ marginTop: '10px', marginBottom: '10px' }}
                        >
                            <FormController
                                controlType='select'
                                name='commonStatus'
                                label='Trạng thái'
                                data={commonStatus}
                                placeholder='Trạng thái'
                            />
                        </Col>
                    )}
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='categoryId'
                            label='Nhóm sản phẩm'
                            data={categories}
                            placeholder='Nhóm sản phẩm'
                            required
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='select'
                            name='supplierId'
                            label='Nhà cung cấp'
                            data={suppliers}
                            placeholder='Nhà cung cấp'
                            required
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>
                    <Col
                        md={6}
                        style={{ marginTop: '10px', marginBottom: '10px' }}
                    >
                        <FormController
                            controlType='textInput'
                            name='productVersion'
                            label='Phiên bản sử dụng'
                            placeholder='Phiên bản sử dụng'
                            readOnly={page === 'chi-tiet'}
                        />
                    </Col>

                    {page === 'chi-tiet' && (
                        <>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='userNameCreated'
                                    label='Người tạo'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>
                            <Col
                                md={6}
                                style={{
                                    marginTop: '10px',
                                    marginBottom: '10px',
                                }}
                            >
                                <FormController
                                    controlType='textInput'
                                    name='createdDateTime'
                                    label='Ngày tạo'
                                    readOnly={page === 'chi-tiet'}
                                />
                            </Col>
                        </>
                    )}
                </Row>
                {page !== 'chi-tiet' && (
                    <Row
                        className='g-3 justify-content-around'
                        style={{ padding: '20px 40px 20px 40px' }}
                    >
                        <Col
                            md='12'
                            className='d-flex align-items-center justify-content-end'
                        >
                            <Button
                                color='danger'
                                className='me-2'
                                type='button'
                                onClick={onClose}
                            >
                                Hủy
                            </Button>
                            <Button
                                color='success'
                                type='button'
                                onClick={methods.handleSubmit(
                                    handleFormSubmit,
                                    (errors) => {
                                        // Show validation errors
                                        const firstError =
                                            Object.values(errors)[0];
                                        if (firstError?.message) {
                                            toast.error(firstError.message);
                                        } else if (firstError) {
                                            toast.error(
                                                'Vui lòng kiểm tra lại thông tin đã nhập',
                                            );
                                        }
                                    },
                                )}
                            >
                                {page === 'tao-moi' ? 'Tạo mới' : 'Lưu'}
                            </Button>
                        </Col>
                    </Row>
                )}
            </Card>
        </FormProvider>
    );
};

export default FormProducts;
